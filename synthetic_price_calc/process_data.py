import functools
import logging
import time
from collections import defaultdict
from datetime import UTC, datetime
from itertools import chain
from multiprocessing.connection import Connection
from typing import Any, cast

import orjson
import pandas as pd
import utils_calc
import utils_general
from utils_calc import Model, ModelParamsBase

from .config import NUM_WORKERS
from .synthetic_calc import (
    calc_synthetic_params,
    refit_svi_to_reduce_calendar_arb,
)
from .typings import (
    DataSetSnap,
    FailedTenorObject,
    ProcessDataResult,
    S3Details,
    SingleTenor,
    SingleTenorResult,
    Snapshot,
    SpotFromManager,
)
from .utils import extract_future_details, put_s3_object

# default='warn'
pd.options.mode.chained_assignment = None  # type: ignore


def process_chunk_helper(
    chunk: list[list[Snapshot]],
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
    **kwargs: Any,  # Added for compatibility with blockstream utils
) -> list[ProcessDataResult]:

    version_prefix = ""

    if version:
        version_prefix = f"{version}."

    flattened_chunk: list[Snapshot] = list(chain.from_iterable(chunk))
    if not flattened_chunk:
        raise ValueError("Chunk is empty!")

    results = []
    failed_snapshots = []
    for snap in flattened_chunk:
        model_results: dict[str, list[SingleTenorResult]] = defaultdict(list)
        failed_model_results: dict[str, list[FailedTenorObject]] = defaultdict(
            list
        )

        try:
            for data in snap.data_snap:
                tenor_result, failed_tenor_info = _process_single_tenor(
                    data=data,
                    target_asset_spot_data=snap.target_asset_spot_data,
                    reference_asset_spot_data=snap.reference_asset_spot_data,
                    target_asset=snap.target_asset,
                    version_prefix=version_prefix,
                    timestamp=snap.timestamp,
                    debug=debug,
                )

                if failed_tenor_info is not None:
                    failed_model_results.setdefault(data.model, []).append(
                        failed_tenor_info
                    )

                if tenor_result is not None:
                    model_results.setdefault(data.model, []).append(
                        tenor_result
                    )

            if "SVI" in model_results:
                """To reduce calendar arbitrage, we need to refit the SVI model"""
                refit_svi_to_reduce_calendar_arb(model_results)

            for model, failed_tenors in failed_model_results.items():
                failed_snapshot_record = {
                    "model": model,
                    "failed_params": orjson.dumps(
                        [res["failed_tenor"] for res in failed_tenors],
                        option=orjson.OPT_SERIALIZE_NUMPY,
                    ).decode("utf-8"),
                    "reference_params": orjson.dumps(
                        [res["reference_tenor"] for res in failed_tenors],
                        option=orjson.OPT_SERIALIZE_NUMPY,
                    ).decode("utf-8"),
                    "timestamp": snap.timestamp,
                    "target_asset": snap.target_asset,
                    "reference_asset": snap.reference_asset,
                    "ref_asset_spot": snap.reference_asset_spot_data,
                    "target_asset_spot": snap.target_asset_spot_data,
                }
                failed_snapshots.append(failed_snapshot_record)

            for model, model_params in model_results.items():
                if model_params:
                    results.append(
                        ProcessDataResult(
                            timestamp=snap.timestamp,
                            params=orjson.dumps(
                                model_params, option=orjson.OPT_SERIALIZE_NUMPY
                            ).decode("utf-8"),
                            qualified_name=f"{version_prefix}blockscholes-syn.option.{snap.target_asset}.{model}.{freq}.params",
                            runtime=utils_general.to_iso(datetime.now(tz=UTC)),
                        )
                    )
                else:
                    logging.error(
                        f"Empty results for {model=}, iso_stamp={utils_general.to_iso(snap.timestamp)}, {snap.timestamp=}, {snap.target_asset=}, {snap.reference_asset=}, {snap.reference_exchange=} "
                    )

        except Exception:
            logging.exception(
                f"Error while processing snapshot iso_stamp={utils_general.to_iso(snap.timestamp)}, {snap.timestamp=}, {snap.target_asset=}, {snap.reference_asset=}, {snap.reference_exchange=}"
            )

    if failed_snapshots:
        try:
            put_s3_object(
                details=s3_details,
                data=pd.DataFrame(failed_snapshots).to_csv(index=False),
                sub_prefix="failed_snapshots",
            )
            logging.info("Stored failed snapshots to S3")
        except Exception:
            logging.exception("Failed to upload invalid snapshots to S3")

    return results


def _process_chunk(
    chunk: list[list[Snapshot]],
    conn: Connection,
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
) -> None:
    try:
        results = process_chunk_helper(
            chunk=chunk,
            freq=freq,
            version=version,
            debug=debug,
            s3_details=s3_details,
        )
        conn.send(results)
    except Exception as e:
        conn.send({"error": str(e), "details": ""})
        logging.exception("Error processing data chunk")
    conn.close()


def process_data(
    data: list[list[Snapshot]],
    freq: str,
    version: str,
    s3_details: S3Details,
    debug: bool = False,
) -> list[ProcessDataResult]:

    t = time.time()
    # process results into params surface
    result: list[ProcessDataResult] = utils_general.parallel_process(
        data_slices=data,
        num_workers=NUM_WORKERS,
        process_chunk_fn=functools.partial(
            _process_chunk,
            freq=freq,
            version=version,
            s3_details=s3_details,
            debug=debug,
        ),
        chunk_data=True,
    )

    print()
    # smooth results


    # refit calendar arb

    try:
        if result:
            result = sorted(result, key=lambda x: x["timestamp"])
    except Exception:
        logging.exception("Error processing data")

    logging.info(f"SyntheticPrice calc took {round(time.time() - t)}s")
    return result


def _process_single_tenor(
    data: DataSetSnap,
    target_asset: str,
    target_asset_spot_data: list[SpotFromManager],
    reference_asset_spot_data: list[SpotFromManager],
    version_prefix: str,
    timestamp: int,
    debug: bool = False,
) -> tuple[SingleTenorResult | None, FailedTenorObject | None]:

    params = data.params
    future = data.future

    qn_freq, expiry_str = extract_future_details(future["qualified_name"])
    try:
        tenor_result, is_successful = calc_synthetic_params(
            ref_asset_daily_spot_pxs=reference_asset_spot_data,
            target_asset_daily_spot_pxs=target_asset_spot_data,
            tenor_days=params["expiry"] * 365,
            ref_params=params,
            model=data.model,
        )
        if not is_successful:
            if debug:
                return None, {
                    "failed_tenor": tenor_result,
                    "reference_tenor": params,
                }
            return None, None

        return (
            cast(
                SingleTenorResult,
                {
                    **tenor_result,
                    "qualified_name": f"{version_prefix}blockscholes-syn.option.{target_asset}.{data.model}.{expiry_str}.{qn_freq}.params",
                    "underlying_index": utils_general.get_qfn_and_version(
                        future["qualified_name"]
                    )[1][-3],
                    "spot": data.spot,
                    "forward": future["px"],
                    "atm_vol": _get_atm_vol(
                        future_px=future["px"],
                        expiry=params["expiry"],
                        model=cast(Model, data.model),
                        params=tenor_result,
                    ),
                },
            ),
            None,
        )

    except Exception:
        logging.exception(
            f"Error while creating synthetic params from {target_asset=} from {params['base']}. iso_stamp={utils_general.to_iso(timestamp)}, {timestamp=}, {expiry_str=}"
        )
        return None, None


def _get_atm_vol(
    future_px: float, expiry: float, model: Model, params: SingleTenor
) -> float:
    return float(
        utils_calc.utils_calc_helpers.model_vol(
            strike=(future_px),
            forward=(future_px),
            exp=expiry,
            model=model,
            model_params=cast(
                ModelParamsBase,
                utils_calc.extract_model_params(params, model),  # type: ignore
            ),
        ),
    )
