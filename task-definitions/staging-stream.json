{"containerDefinitions": [{"name": "syntheticPriceCalcStream-container", "image": "public.ecr.aws/docker/library/python:3.12", "cpu": 0, "essential": true, "environment": [{"name": "MIN_KINESIS_FETCH_DELAY_S", "value": "5"}, {"name": "SSM_INDEX_CONFIG_PATH", "value": "/config/price_indices_generated"}], "mountPoints": [], "volumesFrom": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/syntheticPriceCalcStream-containers-staging", "awslogs-region": "eu-west-2", "awslogs-stream-prefix": "ecs", "awslogs-datetime-format": "%Y-%m-%d %H:%M:%S"}}}], "family": "syntheticPriceCalcStream-task-definition", "taskRoleArn": "arn:aws:iam::273532302533:role/syntheticPriceCalcStreamContainerRole", "executionRoleArn": "arn:aws:iam::273532302533:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "volumes": [], "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"operatingSystemFamily": "LINUX", "cpuArchitecture": "ARM64"}}