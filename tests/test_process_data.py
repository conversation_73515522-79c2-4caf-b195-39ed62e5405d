import json
from typing import Any
from unittest.mock import MagicMock, patch

import orjson
import pytest

from synthetic_price_calc.process_data import process_chunk_helper
from synthetic_price_calc.typings import Snapshot


def round_floats(obj: Any, precision: int = 4) -> Any:
    if isinstance(obj, float):
        return round(obj, precision)
    elif isinstance(obj, dict):
        return {k: round_floats(v, precision) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [round_floats(elem, precision) for elem in obj]
    return obj


@patch(
    "synthetic_price_calc.synthetic_calc.load_ssm_params",
    return_value={"DOMESTIC_RATES": {}},
)
@patch(
    "synthetic_price_calc.synthetic_calc.get_domestic_rate", return_value=0.0
)
def test_process_chunk_helper(
    mock_load_ssm_params: MagicMock,
    mock_get_rate: MagicMock,
    snapshot: Any,
    process_chunk_helper_snapshot: Snapshot,
    request: pytest.FixtureRequest,
) -> None:
    test_case_name = request.node.callspec.params[
        "process_chunk_helper_snapshot"
    ]

    result = process_chunk_helper(
        chunk=[[process_chunk_helper_snapshot]],
        freq="1h",
        version="v-00004",
        s3_details=None,  # type: ignore
    )

    for item in result:
        del item["runtime"]  # type: ignore
        params = item["params"]
        item["params"] = (
            orjson.loads(item["params"]) if isinstance(params, str) else params
        )

    rounded_result = round_floats(result)

    snapshot.assert_match(
        json.dumps(rounded_result, indent=2),
        f"{test_case_name}.json",
    )
